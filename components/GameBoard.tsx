/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from "react";
import {
  Alert,
  Animated,
  Dimensions,
  Image,
  Platform,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import {
  EnhancedGameRoom,
  GameRoom,
  GameService,
  getCardById,
  getCardPoints,
  getCardRanking,
  getPartnerPlayer,
  PlayedCard,
  Player,
} from "../services/gameService";
import BiddingDialog from "./BiddingDialog";
import ScoringValidationDialog from "./ScoringValidationDialog";
import TrumpGameTypeDialog from "./TrumpGameTypeDialog";
import TrumpSelectionDialog from "./TrumpSelectionDialog";

const { width, height } = Dimensions.get("window");

// Card back image
const cardBack = require("../assets/cards/blue_pattern.png");

interface GameBoardProps {
  roomId: string;
  playerId: string;
  onGameEnd: () => void;
}

function GameBoard({ roomId, playerId, onGameEnd }: GameBoardProps) {
  const [gameRoom, setGameRoom] = useState<GameRoom | EnhancedGameRoom | null>(
    null
  );
  const [currentPlayer, setCurrentPlayer] = useState<Player | null>(null);
  const [isTrumpCardVisible, setIsTrumpCardVisible] = useState<boolean>(false);
  const previousTrumpCardId = React.useRef<string | null>(null);

  // Animation for trump reveal - add this new animation
  const [trumpRevealAnimation] = React.useState(new Animated.Value(0));
  const [showTrumpRevealEffect, setShowTrumpRevealEffect] =
    React.useState(false);
  const previousTrumpRevealed = React.useRef<boolean>(false);

  // State to track when center trick is being cleared
  const [isClearingCenterTrick, setIsClearingCenterTrick] = React.useState(false);

  // Animation values for each center position
  const [centerAnimations] = React.useState({
    bottom: new Animated.Value(0),
    right: new Animated.Value(0),
    top: new Animated.Value(0),
    left: new Animated.Value(0),
  });

  // Animation values for clearing/folding cards
  const [clearAnimations] = React.useState({
    bottom: new Animated.Value(0),
    right: new Animated.Value(0),
    top: new Animated.Value(0),
    left: new Animated.Value(0),
  });

  // Animation for turn indicator pulsing
  const [turnPulseAnimation] = React.useState(new Animated.Value(1));

  const cardWidth = 60;
  const cardHeight = 90;

  // Subscribe to game room updates
  useEffect(() => {
    const unsubscribe = GameService.subscribeToRoom(roomId, (room) => {
      if (!room) {
        Alert.alert("Error", "Room not found");
        onGameEnd();
        return;
      }

      // Reset trump card visibility when trump card changes or new round starts
      if (room.trumpCard?.id !== previousTrumpCardId.current) {
        setIsTrumpCardVisible(false);
        previousTrumpCardId.current = room.trumpCard?.id || null;
      }

      // Handle trump reveal animation
      if (room.isTrumpRevealed && !previousTrumpRevealed.current) {
        console.log("🎯 Trump revealed! Triggering animation");
        setShowTrumpRevealEffect(true);
        previousTrumpRevealed.current = true;

        // Animate trump reveal effect - longer duration with pause
        Animated.sequence([
          // Entrance animation - scale up and rotate
          Animated.timing(trumpRevealAnimation, {
            toValue: 1,
            duration: 2000, // Slower entrance - 2 seconds
            useNativeDriver: true,
          }),
          // Hold the reveal for players to see clearly
          Animated.timing(trumpRevealAnimation, {
            toValue: 1,
            duration: 3000, // Hold for 3 seconds so players can read
            useNativeDriver: true,
          }),
          // Exit animation
          Animated.timing(trumpRevealAnimation, {
            toValue: 0,
            duration: 1000, // Slower exit - 1 second
            useNativeDriver: true,
          }),
        ]).start(() => {
          setShowTrumpRevealEffect(false);
          trumpRevealAnimation.setValue(0);
        });
      }

      // Reset trump reveal tracking when a new round starts
      if (!room.isTrumpRevealed && previousTrumpRevealed.current) {
        previousTrumpRevealed.current = false;
      }

      // Reset clearing state when playedCards array is empty (new round/trick)
      if (room.playedCards.length === 0 && isClearingCenterTrick) {
        console.log("🔄 New round/trick started - Resetting isClearingCenterTrick to FALSE");
        setIsClearingCenterTrick(false);
      }

      setGameRoom(room);

      // Find current player
      const player = room.players.find((p) => p.id === playerId);
      setCurrentPlayer(player || null);

      // Debug dialog visibility
      console.log("Game state update:", {
        gameState: room.gameState,
        highestBidder: room.highestBidder,
        playerId: playerId,
        isHighestBidder: room.highestBidder === playerId,
        trumpDialogVisible:
          room.gameState === "trump_selection" &&
          room.highestBidder === playerId,
        cardDialogVisible:
          room.gameState === "trump_type_selection" &&
          room.highestBidder === playerId,
      });
    });

    return unsubscribe;
  }, [roomId, playerId, onGameEnd]);

  // Turn indicator pulse animation
  useEffect(() => {
    if (!gameRoom) return;

    const shouldPulse =
      (gameRoom.gameState === "in_progress" &&
        gameRoom.currentTurn === playerId) ||
      (gameRoom.gameState === "bidding" && gameRoom.biddingTurn === playerId) ||
      (gameRoom.gameState === "trump_selection" &&
        gameRoom.highestBidder === playerId) ||
      (gameRoom.gameState === "trump_type_selection" &&
        gameRoom.highestBidder === playerId);

    if (shouldPulse) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(turnPulseAnimation, {
            toValue: 1.3,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(turnPulseAnimation, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      turnPulseAnimation.setValue(1);
    }
  }, [gameRoom?.currentTurn, gameRoom?.biddingTurn, gameRoom?.gameState]);

  // Function to get perspective-based player positions
  const getPerspectivePositions = () => {
    if (!gameRoom || !currentPlayer) return {};

    // Sort players by their original position to maintain consistent ordering
    const sortedPlayers = [...gameRoom.players].sort((a, b) => {
      const positionOrder = { bottom: 0, right: 1, top: 2, left: 3 };
      return positionOrder[a.position] - positionOrder[b.position];
    });

    // Find current player index
    const currentPlayerIndex = sortedPlayers.findIndex(
      (p) => p.id === playerId
    );

    // Create perspective-based positions
    const perspectivePositions: Record<string, Player> = {};
    const viewPositions = ["bottom", "right", "top", "left"];

    sortedPlayers.forEach((player, index) => {
      // Calculate relative position from current player's perspective
      let relativeIndex =
        (index - currentPlayerIndex + sortedPlayers.length) %
        sortedPlayers.length;

      // Assign to view position
      if (relativeIndex < viewPositions.length) {
        perspectivePositions[viewPositions[relativeIndex]] = player;
      }
    });

    return perspectivePositions;
  };

  // Function to get perspective-based played cards positions
  const getPerspectivePlayedCards = () => {
    if (!gameRoom || !currentPlayer) return {};

    const perspectivePositions = getPerspectivePositions();
    const perspectivePlayedCards: Record<string, any> = {};

    // Map actual played cards to perspective positions
    gameRoom.playedCards.forEach((playedCard) => {
      // Find which perspective position this player occupies
      const perspectivePosition = Object.keys(perspectivePositions).find(
        (pos) => perspectivePositions[pos].id === playedCard.playerId
      );

      if (perspectivePosition) {
        perspectivePlayedCards[perspectivePosition] = playedCard;
      }
    });

    return perspectivePlayedCards;
  };

  // Function to animate cards folding and moving to bottom right corner
  const animateCardClearing = () => {
    const positions = ["bottom", "right", "top", "left"] as const;
    const perspectivePlayedCards = getPerspectivePlayedCards();

    const animations = positions
      .map((position) => {
        const playedCard = perspectivePlayedCards[position];
        if (playedCard) {
          return Animated.timing(clearAnimations[position], {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          });
        }
        return null;
      })
      .filter(
        (animation): animation is Animated.CompositeAnimation =>
          animation !== null
      );

    return Animated.parallel(animations);
  };

  // Handle card animations when cards are played
  React.useEffect(() => {
    if (!gameRoom) return;

    const playedCount = gameRoom.playedCards.length;
    const currentRoundExpectedCards = gameRoom.players.length;

    // Reset clearing state if playedCards is empty (new trick started)
    if (playedCount === 0 && isClearingCenterTrick) {
      console.log("🔄 New trick started (playedCards empty) - Resetting isClearingCenterTrick to FALSE");
      setIsClearingCenterTrick(false);
      return;
    }

    // Animate entrance of the latest played card
    if (playedCount > 0) {
      const lastPlayedCard =
        gameRoom.playedCards[gameRoom.playedCards.length - 1];
      if (lastPlayedCard) {
        // Find perspective position for this player
        const perspectivePositions = getPerspectivePositions();
        const perspectivePosition = Object.keys(perspectivePositions).find(
          (pos) => perspectivePositions[pos].id === lastPlayedCard.playerId
        );

        if (perspectivePosition) {
          centerAnimations[
            perspectivePosition as keyof typeof centerAnimations
          ].setValue(0);
          Animated.spring(
            centerAnimations[
              perspectivePosition as keyof typeof centerAnimations
            ],
            {
              toValue: 1,
              useNativeDriver: true,
              tension: 100,
              friction: 8,
            }
          ).start();
        }
      }
    }

    // If all players have played their cards for the current round, clear them after delay
    if (playedCount === currentRoundExpectedCards && playedCount > 0 && !isClearingCenterTrick) {
      // Immediately set clearing state to prevent card clicks during both waiting period and animation
      console.log("🚫 All cards played - Setting isClearingCenterTrick to TRUE (preventing card clicks)");
      setIsClearingCenterTrick(true);

      // Wait 3 seconds then start clearing animation
      const timer = setTimeout(() => {
        console.log("🎬 Starting center trick clearing animation");
        animateCardClearing().start((finished) => {
          // After animation completes, reset everything
          console.log("🎬 Animation finished:", finished);
          Object.values(centerAnimations).forEach((anim) => anim.setValue(0));
          Object.values(clearAnimations).forEach((anim) => anim.setValue(0));

          // Reset clearing state to allow card clicks again
          console.log("✅ Center trick cleared - Setting isClearingCenterTrick to FALSE (allowing card clicks)");
          setIsClearingCenterTrick(false);
        });

        // Fallback timeout to ensure clearing state is reset even if animation fails
        const fallbackTimer = setTimeout(() => {
          console.log("⚠️ Fallback timeout - Forcing isClearingCenterTrick to FALSE");
          setIsClearingCenterTrick(false);
        }, 5000); // 5 seconds after animation starts

        return () => clearTimeout(fallbackTimer);
      }, 3000);

      return () => {
        console.log("🧹 Cleaning up timer");
        clearTimeout(timer);
      };
    }
  }, [gameRoom?.playedCards, isClearingCenterTrick]);

  // Function to determine which card image to show in player hands
  const getCardImage = (player: Player, cardIndex: number) => {
    // Show actual card only if the player ID matches logged in user ID
    if (player.id === playerId) {
      const card = player.cards[cardIndex];
      return card ? getCardById(card.id).image : cardBack;
    }
    // Otherwise show card back
    return cardBack;
  };

  // Function to get played card image considering face-down rules
  const getPlayedCardImage = (playedCard: PlayedCard) => {
    if (!gameRoom) return cardBack;

    const isTrumpMaker = gameRoom.highestBidder === playerId;
    const isOwnCard = playedCard.playerId === playerId;
    const card = getCardById(playedCard.cardId);

    // Always show your own cards
    if (isOwnCard) {
      return card.image;
    }

    // If trump is revealed (open trump), show all cards
    if (gameRoom.isTrumpRevealed) {
      return card.image;
    }

    // If trump is not revealed (closed trump scenario):
    if (!gameRoom.isTrumpRevealed) {
      // If card was played face down (because player couldn't follow suit)
      if (playedCard.isFaceDown) {
        // Trump maker can see all face-down cards for inspection
        if (isTrumpMaker) {
          return card.image;
        }
        // Other players see card back for face-down cards
        else {
          return cardBack;
        }
      }
      // If card was played face up (player could follow suit)
      else {
        return card.image;
      }
    }

    // Default fallback
    return cardBack;
  };

  // Function to check which cards are playable based on suit following rules
  const getPlayableCards = () => {
    // Debug logging to track when cards are disabled
    if (isClearingCenterTrick) {
      console.log("🚫 getPlayableCards: Cards disabled due to isClearingCenterTrick =", isClearingCenterTrick);
    }

    if (
      !gameRoom ||
      !currentPlayer ||
      gameRoom.gameState !== "in_progress" ||
      gameRoom.currentTurn !== playerId ||
      isClearingCenterTrick // Prevent card play during center trick clearing
    ) {
      return new Set<string>();
    }

    const playableCards = new Set<string>();

    // Get current trick cards for validation
    const validationTrickSize =
      gameRoom.playedCards.length % gameRoom.players.length;
    const validationTrickStart =
      Math.floor(gameRoom.playedCards.length / gameRoom.players.length) *
      gameRoom.players.length;
    const validationTrickCards =
      gameRoom.playedCards.slice(validationTrickStart);

    // If it's the first card of the trick, any card can be played
    if (validationTrickCards.length === 0) {
      currentPlayer.cards.forEach((card) => playableCards.add(card.id));
      return playableCards;
    }

    // Get the suit that was led
    const leadCard = validationTrickCards[0];
    const leadCardData = getCardById(leadCard.cardId);
    const ledSuit = leadCardData.suit;

    // Check which cards can follow suit
    const cardsOfLedSuit = currentPlayer.cards.filter(
      (card) => card.suit === ledSuit
    );

    // 304 SPECIAL RULE: Trump indicator card restrictions only apply in closed trump games
    // When trump is revealed, all trump cards can follow suit normally
    let effectiveCardsOfLedSuit = cardsOfLedSuit;

    // 304 RULE: When trump is revealed, all trump cards can be played normally
    if (gameRoom.isTrumpRevealed) {
      // Trump is revealed - all trump cards can follow suit normally, including trump indicator
      effectiveCardsOfLedSuit = cardsOfLedSuit;
      console.log(
        "🎯 Client: Trump revealed - all trump cards can follow suit normally"
      );
    }
    // Only apply trump indicator exclusion in closed trump games when trump is not revealed
    else if (
      gameRoom.trumpGameType === "closed" &&
      gameRoom.highestBidder === playerId &&
      ledSuit === gameRoom.trumpSuit
    ) {
      // In closed trump games, trump indicator card cannot follow trump suit
      effectiveCardsOfLedSuit = cardsOfLedSuit.filter(
        (card) => card.id !== gameRoom.trumpIndicatorCardId
      );
      console.log(
        "🎯 Client: Trump led in closed game - excluding trump indicator from follow suit check"
      );
    }

    if (effectiveCardsOfLedSuit.length > 0) {
      // Must follow suit - only cards of the led suit are playable (excluding trump indicator if applicable)
      effectiveCardsOfLedSuit.forEach((card) => playableCards.add(card.id));
    } else {
      // Cannot follow suit - apply 304 rules based on game state

      // 304 RULE: When trump is revealed, standard trick-taking rules apply
      if (gameRoom.isTrumpRevealed) {
        // Trump is revealed - any card can be played when unable to follow suit
        // This includes trump cards for cutting, non-trump cards for throwing
        // Only trump indicator card has its own specific restrictions (handled in validation)
        currentPlayer.cards.forEach((card) => playableCards.add(card.id));
        console.log(
          "🎯 Client: Trump revealed - all cards playable when unable to follow suit"
        );
      }
      // Special rules for closed trump games when trump is not revealed
      else if (
        gameRoom.trumpGameType === "closed" &&
        gameRoom.highestBidder === playerId &&
        !gameRoom.isTrumpRevealed
      ) {
        // 304 RULE: Trump maker in closed trump game can:
        // 1. Cut with trump indicator card (if it's not a trump trick) - special restrictions
        // 2. Play other trump cards normally (face down)
        // 3. Throw non-trump cards (face down)
        //
        // IMPORTANT: Only the trump indicator card has special restrictions,
        // other trump cards can be played normally when unable to follow suit

        const isTrumpLed = ledSuit === gameRoom.trumpSuit;

        currentPlayer.cards.forEach((card) => {
          const isTrumpIndicatorCard =
            card.id === gameRoom.trumpIndicatorCardId;

          // 304 RULE: Trump indicator card can only be played:
          // 1. Face down to cut a NON-trump trick, OR
          // 2. In the eighth trick as the only remaining card
          if (isTrumpIndicatorCard && isTrumpLed) {
            // Cannot cut trump trick with trump indicator card in closed games
            // This card is not playable - skip it
            return;
          }

          // All other cards are playable when unable to follow suit:
          // - Trump indicator card (if not cutting trump trick)
          // - Other trump cards (played face down)
          // - Non-trump cards (played face down)
          playableCards.add(card.id);
        });
      } else {
        // Normal rules - cannot follow suit, any card can be played
        currentPlayer.cards.forEach((card) => playableCards.add(card.id));
      }
    }
    return playableCards;
  };

  // Function to handle card click with animation
  const handleCardClick = async (cardIndex: number) => {
    if (!gameRoom || !currentPlayer) return;

    // Prevent card clicks during center trick clearing
    if (isClearingCenterTrick) {
      console.log("🚫 Card click blocked - Center trick is being cleared");
      Alert.alert("Please Wait", "Cards are being cleared from the center. Please wait a moment before playing your next card.");
      return;
    }

    // Handle trump card selection (after trump game type is chosen)
    if (gameRoom.gameState === "trump_type_selection") {
      await handleTrumpCardClick(cardIndex);
      return;
    }

    // Handle regular card play
    if (gameRoom.gameState !== "in_progress") {
      Alert.alert("Game Not Ready", "Wait for the game to start");
      return;
    }

    // Check if it's the player's turn
    if (gameRoom.currentTurn !== playerId) {
      Alert.alert("Not Your Turn", "Please wait for your turn to play a card");
      return;
    }

    const cardToPlay = currentPlayer.cards[cardIndex];
    if (!cardToPlay) return;

    // Check if this card is playable according to suit following rules
    const playableCards = getPlayableCards();
    if (!playableCards.has(cardToPlay.id)) {
      // Get current trick cards to show which suit to follow
      const validationTrickSize =
        gameRoom.playedCards.length % gameRoom.players.length;
      const validationTrickStart =
        Math.floor(gameRoom.playedCards.length / gameRoom.players.length) *
        gameRoom.players.length;
      const validationTrickCards =
        gameRoom.playedCards.slice(validationTrickStart);

      if (validationTrickCards.length > 0) {
        const leadCard = validationTrickCards[0];
        const leadCardData = getCardById(leadCard.cardId);
        const cardsOfLedSuit = currentPlayer.cards.filter(
          (card) => card.suit === leadCardData.suit
        );

        Alert.alert(
          "Must Follow Suit",
          `You must play a ${leadCardData.suit} card. You have ${cardsOfLedSuit.length} ${leadCardData.suit} card(s) to choose from.`,
          [{ text: "OK" }]
        );
      }
      return;
    }

    try {
      await GameService.playCard(roomId, playerId, cardToPlay.id);
      // Animation will be triggered by the useEffect when gameRoom state updates
    } catch (error: any) {
      console.error("Error playing card:", error);

      // Special handling for 304 closed trump game restrictions
      if (error.message && error.message.includes("304 Rule:")) {
        Alert.alert(
          "304 Closed Trump Game Rule",
          error.message +
            "\n\n📖 According to 304 rules:\n• When you cannot follow suit in a closed trump game\n• You can only cut with the trump indicator card (face down)\n• Or throw non-trump cards (face down)\n• Other trump cards cannot be played until trump is revealed",
          [{ text: "Understood" }]
        );
      }
      // Special handling for trump indicator card errors
      else if (
        error.message &&
        error.message.includes("Trump indicator card")
      ) {
        Alert.alert(
          "Trump Indicator Card Restriction",
          error.message +
            "\n\nThe trump indicator card can only be played:\n• Face down to cut a non-trump trick (when you cannot follow suit)\n• In the eighth trick as your only remaining card",
          [{ text: "Understood" }]
        );
      } else if (error.message && error.message.includes("follow suit")) {
        Alert.alert("Suit Following Rule", error.message, [{ text: "OK" }]);
      } else {
        Alert.alert("Error", error.message || "Failed to play card");
      }
    }
  };

  const leaveGame = async () => {
    try {
      await GameService.leaveRoom(roomId, playerId);
      onGameEnd();
    } catch (error) {
      console.error("Error leaving game:", error);
      onGameEnd();
    }
  };

  // Handle bidding
  const handleSubmitBid = async (bidAmount: number) => {
    try {
      await GameService.submitBid(roomId, playerId, bidAmount);
    } catch (error: any) {
      console.error("Error submitting bid:", error);
      Alert.alert("Error", error.message || "Failed to submit bid");
    }
  };

  const handlePassBid = async () => {
    try {
      await GameService.passBid(roomId, playerId);
    } catch (error: any) {
      console.error("Error passing bid:", error);
      Alert.alert("Error", error.message || "Failed to pass bid");
    }
  };

  // Handle trump selection
  const handleSelectTrump = async (cardId: string) => {
    try {
      await GameService.selectTrumpCard(roomId, playerId, cardId);
    } catch (error: any) {
      console.error("Error selecting trump:", error);
      Alert.alert("Error", error.message || "Failed to select trump card");
    }
  };

  // Handle trump card click during trump card selection
  const handleTrumpCardClick = async (cardIndex: number) => {
    if (!gameRoom || !currentPlayer) return;

    if (gameRoom.gameState !== "trump_type_selection") {
      Alert.alert(
        "Not Trump Card Selection Phase",
        "You can only select trump cards during trump card selection phase"
      );
      return;
    }

    if (gameRoom.highestBidder !== playerId) {
      Alert.alert("Not Your Turn", "Only the highest bidder can select trump");
      return;
    }

    const cardToSelect = currentPlayer.cards[cardIndex];
    if (!cardToSelect) return;

    await handleSelectTrump(cardToSelect.id);
  };

  // Handle manual trump reveal
  const handleRevealTrump = async () => {
    try {
      await GameService.revealTrump(roomId, playerId);
    } catch (error: any) {
      console.error("Error revealing trump:", error);
      Alert.alert("Error", error.message || "Failed to reveal trump");
    }
  };

  const handleSelectTrumpGameType = async (gameType: any) => {
    if (!gameRoom || !currentPlayer) {
      console.log("Missing gameRoom or currentPlayer:", {
        gameRoom: !!gameRoom,
        currentPlayer: !!currentPlayer,
      });
      return;
    }

    console.log("Selecting trump game type:", gameType);
    try {
      await GameService.selectTrumpGameType(gameRoom.id, playerId, gameType);
      console.log("Trump game type selected successfully");
    } catch (error: any) {
      console.error("Error selecting trump game type:", error);
      Alert.alert("Error", error.message || "Failed to select trump game type");
    }
  };

  // Handle automatic next round progression
  const handleAutoNextRound = async () => {
    try {
      await GameService.processScoringDecision(roomId, 'next_round', playerId);
    } catch (error: any) {
      console.error("Error processing automatic next round:", error);
      Alert.alert("Error", error.message || "Failed to start next round");
    }
  };

  // Handle trump suit change (for maker's choice)
  const handleChangeTrump = async () => {
    try {
      // For now, just show an alert. In a full implementation,
      // you'd show a suit selection dialog
      Alert.alert("Change Trump", "Trump suit change feature coming soon!");
    } catch (error: any) {
      console.error("Error changing trump:", error);
      Alert.alert("Error", error.message || "Failed to change trump");
    }
  };

  // Handle trump card toggle for bid winner
  const handleTrumpCardToggle = () => {
    if (gameRoom?.highestBidder === playerId) {
      setIsTrumpCardVisible(!isTrumpCardVisible);
    }
  };

  const handleInspectFaceDownCards = async () => {
    if (!gameRoom || !currentPlayer) return;

    try {
      const result = await GameService.inspectFaceDownCards(
        gameRoom.id,
        playerId
      );

      if (result.hasTrumps) {
        Alert.alert(
          "Trump Cards Found!",
          `Found ${result.cards.length} face-down card(s), including trump cards. Trump has been automatically revealed!`,
          [{ text: "OK" }]
        );
      } else {
        Alert.alert(
          "Face-down Cards Inspected",
          `Inspected ${result.cards.length} face-down card(s). No trump cards found.`,
          [{ text: "OK" }]
        );
      }
    } catch (error: any) {
      console.error("Error inspecting face-down cards:", error);
      Alert.alert(
        "Error",
        error.message || "Failed to inspect face-down cards"
      );
    }
  };

  // Function to get trump suit icon
  const getTrumpSuitIcon = (suit: string) => {
    const icons = {
      hearts: "♥️",
      diamonds: "♦️",
      clubs: "♣️",
      spades: "♠️",
    };
    return icons[suit as keyof typeof icons] || "🃏";
  };

  // Function to check if a card is trump
  const isCardTrump = (card: any) => {
    if (!gameRoom?.trumpSuit || gameRoom.trumpGameType === "no_trump")
      return false;
    const cardData = typeof card === "string" ? getCardById(card) : card;
    return cardData.suit === gameRoom.trumpSuit;
  };

  // Function to check if a card is the trump indicator card
  const isTrumpIndicatorCard = (cardId: string) => {
    return gameRoom?.trumpIndicatorCardId === cardId;
  };

  // Function to get card point value for display
  const getCardPointValue = (cardId: string) => {
    const card = getCardById(cardId);
    return getCardPoints(card.rank);
  };

  // Function to sort player hand cards by suit and rank (highest to lowest, right to left)
  const sortPlayerCards = (cards: any[]) => {
    if (!cards || cards.length === 0) return cards;

    // Define suit order (you can adjust this based on preference)
    const suitOrder = ["spades", "hearts", "diamonds", "clubs"];

    // Sort cards by suit first, then by rank (highest to lowest)
    return [...cards].sort((a, b) => {
      const cardA = getCardById(a.id);
      const cardB = getCardById(b.id);

      // First sort by suit
      const suitIndexA = suitOrder.indexOf(cardA.suit);
      const suitIndexB = suitOrder.indexOf(cardB.suit);

      if (suitIndexA !== suitIndexB) {
        return suitIndexA - suitIndexB;
      }

      // If same suit, sort by rank (highest to lowest)
      const rankA = getCardRanking(cardA.rank);
      const rankB = getCardRanking(cardB.rank);

      return rankB - rankA; // Descending order (highest to lowest)
    });
  };

  // Function to render card with enhanced UI
  const renderEnhancedCard = (
    cardId: string | undefined,
    index: number,
    player: Player,
    position: string,
    isPlayable: boolean = false,
    onPress?: () => void
  ) => {
    if (!cardId || !gameRoom) return null;

    const card = getCardById(cardId);
    const pointValue = getCardPointValue(cardId);
    const isTrump = isCardTrump(card);
    const isTrumpIndicator = isTrumpIndicatorCard(cardId);
    const showActualCard = player.id === playerId; // Only show actual cards for current player

    const cardStyle = {
      width:
        position === "right" || position === "left"
          ? cardHeight * 1.2
          : cardWidth * 1.2,
      height:
        position === "right" || position === "left"
          ? cardWidth * 1.2
          : cardHeight * 1.2,
      transform: [
        {
          rotate:
            position === "top"
              ? "180deg"
              : position === "right"
              ? "90deg"
              : position === "left"
              ? "-90deg"
              : "0deg",
        },
      ],
      opacity: isPlayable ? 1 : 0.85,
    };

    return (
      <TouchableOpacity
        key={`${position}-${index}`}
        onPress={onPress}
        disabled={!isPlayable}
        className="relative"
      >
        {/* Card Image */}
        <Image
          source={showActualCard ? card.image : cardBack}
          style={cardStyle}
          resizeMode="contain"
        />

        {/* Trump Indicator Card Special Indicator */}
        {/* {isTrumpIndicator && showActualCard && (
          <View
            className="absolute top-0 left-0 bg-red-500 rounded-full p-1 z-20"
            style={{
              transform: [
                { rotate: position === 'top' ? '180deg' : position === 'right' ? '90deg' : position === 'left' ? '-90deg' : '0deg' }
              ]
            }}
          >
            <Text className="text-xs">🎯</Text>
          </View>
        )} */}

        {/* Trump Suit Indicator */}
        {/* {isTrump && showActualCard && gameRoom.isTrumpRevealed && !isTrumpIndicator && (
          <View
            className="absolute top-0 right-0 bg-yellow-400 rounded-full p-1 z-10"
            style={{
              transform: [
                { rotate: position === 'top' ? '180deg' : position === 'right' ? '90deg' : position === 'left' ? '-90deg' : '0deg' }
              ]
            }}
          >
            <Text className="text-xs">👑</Text>
          </View>
        )} */}

        {/* Card Point Value */}
        {/* {showActualCard && pointValue > 0 && (
          <View
            className={`absolute bottom-0 left-0 ${isTrump && gameRoom.isTrumpRevealed ? 'bg-yellow-600' : 'bg-blue-600'} rounded-tr-lg px-1 z-10`}
            style={{
              transform: [
                { rotate: position === 'top' ? '180deg' : position === 'right' ? '90deg' : position === 'left' ? '-90deg' : '0deg' }
              ]
            }}
          >
            <Text className="text-white text-xs font-bold">{pointValue}</Text>
          </View>
        )} */}

        {/* Playability Indicator */}
        {/* {isPlayable && (
          <View className="absolute inset-0 border-2 border-green-400 rounded-lg">
            <View className="absolute top-1 left-1 bg-green-400 rounded-full p-1">
              <Text className="text-xs">✓</Text>
            </View>
          </View>
        )} */}
      </TouchableOpacity>
    );
  };

  // Function to render played card with enhanced UI
  const renderPlayedCard = (playedCard: PlayedCard, position: string) => {
    const card = getCardById(playedCard.cardId);
    const pointValue = getCardPointValue(playedCard.cardId);
    const isTrump = isCardTrump(card);
    const isOwnCard = playedCard.playerId === playerId;
    const isTrumpMaker = gameRoom?.highestBidder === playerId;

    // Determine if card should be shown using the same logic as getPlayedCardImage
    let shouldShowCard = false;

    // Always show your own cards
    if (isOwnCard) {
      shouldShowCard = true;
    }
    // If trump is revealed (open trump), show all cards
    else if (gameRoom?.isTrumpRevealed) {
      shouldShowCard = true;
    }
    // If trump is not revealed (closed trump scenario):
    else if (!gameRoom?.isTrumpRevealed) {
      // If card was played face down (because player couldn't follow suit)
      if (playedCard.isFaceDown) {
        // Trump maker can see all face-down cards for inspection
        shouldShowCard = isTrumpMaker;
      }
      // If card was played face up (player could follow suit)
      else {
        shouldShowCard = true;
      }
    }

    return (
      <View className="relative">
        <Image
          source={shouldShowCard ? card.image : cardBack}
          style={{
            width: cardWidth * 1.2,
            height: cardHeight * 1.2,
            transform: [{ rotate: "0deg" }],
          }}
          resizeMode="contain"
        />

        {/* Trump indicator for played cards */}
        {isTrump && shouldShowCard && gameRoom?.isTrumpRevealed && (
          <View className="absolute top-1 left-1 bg-yellow-400 rounded-full p-1">
            <Text className="text-xs">👑</Text>
          </View>
        )}

        {/* Point value for played cards */}
        {shouldShowCard && pointValue > 0 && (
          <View
            className={`absolute bottom-1 right-1 ${
              isTrump ? "bg-yellow-600" : "bg-blue-600"
            } rounded px-1`}
          >
            <Text className="text-white text-xs font-bold">{pointValue}</Text>
          </View>
        )}

        {/* Face-down indicator - show when card was played face down (couldn't follow suit) */}
        {playedCard.isFaceDown && !shouldShowCard && (
          <View className="absolute top-1 right-1 bg-red-500 rounded-full px-2 py-1">
            <Text className="text-white text-xs font-bold">🔒</Text>
          </View>
        )}

        {/* Player indicator */}
        <View className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 bg-black/70 rounded px-2 py-1">
          <Text className="text-white text-xs">
            {gameRoom?.players
              .find((p) => p.id === playedCard.playerId)
              ?.name?.slice(0, 3)}
          </Text>
        </View>
      </View>
    );
  };

  const isIPad = () => {
    if (Platform.OS !== "ios") {
      return false;
    }

    // The 'pad' idiom is specific to iPads.
    if (Platform.constants.interfaceIdiom === "pad") {
      return true;
    }

    // Fallback for older systems or edge cases, though less reliable.
    const { height, width } = Dimensions.get("window");
    const aspectRatio = height / width;
    console.log('isIpad', aspectRatio);
    
    return aspectRatio < 1.6;
  };

  const isIPhone = () => {
    const { height, width } = Dimensions.get("window");
    return Platform.OS === "ios" && height < 800; // Most iPhones have aspect ratios above this
  };

  // Platform-specific styles
  const getLeftPlayerPositionStyle = () => {
    // if (Platform.OS === 'web') {
    //   return "absolute left-0 top-[30%] transform -translate-y-1/2"; // Different values for web
    // }
    return "absolute left-[20%] top-[25%] transform -translate-y-1/2"; // Keep existing values for iOS/Android
  };

  if (!gameRoom || !currentPlayer) {
    return (
      <View className="flex-1 justify-center items-center">
        <Text className="text-lg">Loading game...</Text>
      </View>
    );
  }

  // Get players by perspective position for rendering
  const playersByPosition = getPerspectivePositions();

  // Get played cards by perspective position
  const playedCardsByPosition = getPerspectivePlayedCards();

  // Helper function to get player display name with enhanced indicators
  const getPlayerDisplayName = (player: Player, position: string) => {
    if (!gameRoom) return player.name;

    const isCurrentPlayer = player.id === playerId;
    const isPartner =
      gameRoom.partnership &&
      getPartnerPlayer(playerId, gameRoom.players)?.id === player.id;
    const isCurrentTurn = gameRoom.currentTurn === player.id;
    const isBiddingTurn =
      gameRoom.gameState === "bidding" && gameRoom.biddingTurn === player.id;
    const isTrumpMaker = gameRoom.highestBidder === player.id;

    let suffix = "";
    if (isCurrentPlayer) {
      suffix = " (You)";
    } else if (isPartner) {
      suffix = " (Partner)";
    }

    let prefix = "";
    if (isTrumpMaker) {
      prefix = "👑 ";
    }
    if (isCurrentTurn || isBiddingTurn) {
      prefix += "▶ ";
    }

    return `${prefix}${player.name}${suffix}`;
  };

  // Helper function to get player's bid information
  const getPlayerBidInfo = (playerId: string) => {
    if (!gameRoom?.bids || gameRoom.bids.length === 0) return null;

    // Find the player's most recent bid
    const playerBids = gameRoom.bids.filter((bid) => bid.playerId === playerId);
    if (playerBids.length === 0) return null;

    const latestBid = playerBids[playerBids.length - 1];
    return latestBid;
  };

  // Helper function to get enhanced player name display with styling
  const getEnhancedPlayerDisplay = (player: Player) => {
    if (!gameRoom) return null;

    const isCurrentPlayer = player.id === playerId;
    const isPartner =
      gameRoom.partnership &&
      getPartnerPlayer(playerId, gameRoom.players)?.id === player.id;
    const isCurrentTurn = gameRoom.currentTurn === player.id;
    const isBiddingTurn =
      gameRoom.gameState === "bidding" && gameRoom.biddingTurn === player.id;
    const isTrumpMaker = gameRoom.highestBidder === player.id;
    const isActivePlayer = isCurrentTurn || isBiddingTurn;
    const playerBid = getPlayerBidInfo(player.id);

    return (
      <Animated.View
        className={`px-2 py-1 rounded ${
          isCurrentPlayer
            ? "bg-blue-100 border border-blue-300"
            : isPartner
            ? "bg-green-100 border border-green-300"
            : "bg-white/80"
        }`}
        style={{
          transform: [{ scale: isActivePlayer ? turnPulseAnimation : 1 }],
        }}
      >
        <View className="flex-row items-center justify-center">
          {isTrumpMaker && <Text className="text-xs mr-1">👑</Text>}
          {isActivePlayer && (
            <Text className="text-xs mr-1 text-green-600">▶</Text>
          )}
          <Text
            className={`text-xs text-center ${
              isCurrentPlayer
                ? "text-blue-800 font-bold"
                : isPartner
                ? "text-green-800 font-semibold"
                : "text-gray-700"
            }`}
          >
            {isIPad()} {player.name}
            {isCurrentPlayer
              ? " (You) - "
              : isPartner
              ? " (Partner) - "
              : " - "}
            {playerBid && (
              <Text className="text-xs text-gray-600 ml-1">
                {playerBid.isPassed ? "PASS" : `BID : ${playerBid.amount}`}
              </Text>
            )}
          </Text>
        </View>

        {/* Additional player info */}
        {/* {(player.tricksWon !== undefined && player.tricksWon > 0) && (
          <Text className="text-xs text-center text-gray-600">
            Tricks: {player.tricksWon}
          </Text>
        )} */}
      </Animated.View>
    );
  };

  return (
    <SafeAreaView className="flex-1 relative">
      {/* Consolidated Top Header - Combines Partnership, Game Info, and Points */}
      <View className="absolute top-4 left-4 right-4 z-10">
        {/* <View className="bg-white/95 rounded-xl p-3 shadow-lg border border-gray-200"> */}
        <View className="rounded-xl">
          {/* Partnership Display - Compact Row */}
          {gameRoom.partnership && (
            <View className="flex-row justify-between gap-[300px] items-center mb-2 pb-2">
              <View className="flex-1 items-center">
                <Text className="text-xs font-bold text-blue-600">
                  {gameRoom.partnership.team1.name}
                </Text>
                <Text className="text-xs text-gray-600">
                  {gameRoom.partnership.team1.playerIds
                    .map(
                      (id) => gameRoom.players.find((p) => p.id === id)?.name
                    )
                    .join(" & ")}
                </Text>
                <Text className="text-xs text-blue-800">
                  {gameRoom.partnership.team1.score} |{" "}
                  {gameRoom.partnership.team1.tricksWon} tricks
                </Text>
              </View>
              {/* <View className="text-center px-2">
                <Text className="text-sm font-bold">VS</Text>
              </View> */}
              <View className="flex-1 items-center">
                <Text className="text-xs font-bold text-red-600">
                  {gameRoom.partnership.team2.name}
                </Text>
                <Text className="text-xs text-gray-600">
                  {gameRoom.partnership.team2.playerIds
                    .map(
                      (id) => gameRoom.players.find((p) => p.id === id)?.name
                    )
                    .join(" & ")}
                </Text>
                <Text className="text-xs text-red-800">
                  {gameRoom.partnership.team2.score} |{" "}
                  {gameRoom.partnership.team2.tricksWon} tricks
                </Text>
              </View>
            </View>
          )}

          {/* Game Status Row */}
          <View className="flex-row justify-between items-center">
            <View className="flex-1 ml-20 mb-10">
              <Text className="text-sm font-semibold">
                Round{" "}
                {(gameRoom as EnhancedGameRoom)?.gameFlow?.currentRound
                  ?.roundNumber || gameRoom.round}
              </Text>
              {/* <Text className="text-sm font-semibold">Round {gameRoom.round} • Room: {roomId}</Text> */}
              {gameRoom.gameState === "bidding" && (
                <Text className="text-xs text-blue-600">
                  Bidding:{" "}
                  {gameRoom.currentBid > 0 ? gameRoom.currentBid : "No bids"}
                  {gameRoom.highestBidder &&
                    ` (${
                      gameRoom.players.find(
                        (p) => p.id === gameRoom.highestBidder
                      )?.name
                    })`}
                </Text>
              )}
              {gameRoom.gameState === "trump_selection" && (
                <Text className="text-xs text-blue-600">
                  Selecting Trump Game Type
                </Text>
              )}
              {gameRoom.gameState === "trump_type_selection" && (
                <Text className="text-xs text-blue-600">
                  Selecting Trump Card
                </Text>
              )}
              {gameRoom.lastTrickWinner && (
                <Text className="text-xs text-green-600">
                  Last:{" "}
                  {
                    gameRoom.players.find(
                      (p) => p.id === gameRoom.lastTrickWinner
                    )?.name
                  }
                </Text>
              )}
            </View>

            {/* Status and Points */}
            <View className="items-end">
              <Text className="text-xs font-semibold">
                {gameRoom.gameState === "bidding"
                  ? gameRoom.biddingTurn === playerId
                    ? "Your Bid"
                    : "Waiting..."
                  : gameRoom.gameState === "trump_selection"
                  ? gameRoom.highestBidder === playerId
                    ? "Choose Type"
                    : "Waiting..."
                  : gameRoom.gameState === "trump_type_selection"
                  ? gameRoom.highestBidder === playerId
                    ? "Choose Card"
                    : "Waiting..."
                  : gameRoom.currentTurn === playerId
                  ? "Your Turn"
                  : "Waiting..."}
              </Text>
              {gameRoom.partnership && (
                <Text className="text-xs text-gray-600">
                  Points: {gameRoom.partnership.team1.tricksWon * 38}-
                  {gameRoom.partnership.team2.tricksWon * 38}
                </Text>
              )}
              <TouchableOpacity
                onPress={leaveGame}
                className="bg-red-500 px-2 py-1 rounded mt-1"
              >
                <Text className="text-white text-xs">Leave</Text>
              </TouchableOpacity>
              {/* Debug button to manually reset clearing state */}
              {isClearingCenterTrick && (
                <TouchableOpacity
                  onPress={() => {
                    console.log("🔧 Manual reset - Setting isClearingCenterTrick to FALSE");
                    setIsClearingCenterTrick(false);
                  }}
                  className="bg-orange-500 px-2 py-1 rounded mt-1"
                >
                  <Text className="text-white text-xs">Reset</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>
      </View>

      {/* Trump Information - Compact Side Panel */}
      {/* {(gameRoom.trumpSuit || gameRoom.trumpGameType === 'no_trump') && (
        <View className="absolute top-20 right-4 z-20 max-w-[200px]">
          <View className="bg-yellow-50/95 rounded-lg p-2 border-2 border-yellow-400 shadow-lg">
            <View className="flex-row items-center">
              <Text className="text-xs font-bold text-yellow-800">
                {gameRoom.trumpGameType === 'no_trump'
                  ? '🚫 No Trump'
                  : `${getTrumpSuitIcon(gameRoom.trumpSuit!)} ${gameRoom.trumpSuit?.toUpperCase()}`
                }
              </Text>
              <View className={`ml-1 px-1 py-0.5 rounded ${gameRoom.isTrumpRevealed ? 'bg-green-500' : 'bg-red-500'
                }`}>
                <Text className="text-white text-xs">
                  {gameRoom.isTrumpRevealed ? '🔓' : '🔒'}
                </Text>
              </View>
            </View>

            <Text className="text-xs text-gray-600">
              {gameRoom.trumpGameType?.replace('_', ' ').toUpperCase()}
            </Text>

            {gameRoom.highestBidder === playerId && (
              <View className="flex-row flex-wrap mt-1">
                {!gameRoom.isTrumpRevealed &&
                  (gameRoom.trumpGameType === 'closed' ||
                    gameRoom.trumpGameType === 'maker_choice' ||
                    gameRoom.trumpGameType === 'blind_trump') && (
                    <TouchableOpacity
                      onPress={handleRevealTrump}
                      className="bg-blue-500 px-1 py-0.5 rounded mr-1"
                    >
                      <Text className="text-white text-xs">Reveal</Text>
                    </TouchableOpacity>
                  )}

                {gameRoom.canInspectFaceDown && (
                  <TouchableOpacity
                    onPress={handleInspectFaceDownCards}
                    className="bg-orange-500 px-1 py-0.5 rounded mr-1"
                  >
                    <Text className="text-white text-xs">Inspect</Text>
                  </TouchableOpacity>
                )}

                {gameRoom.trumpGameType === 'maker_choice' &&
                  gameRoom.trumpSpecialRules?.canChangeTrump && (
                    <TouchableOpacity
                      onPress={handleChangeTrump}
                      className="bg-purple-500 px-1 py-0.5 rounded mr-1"
                    >
                      <Text className="text-white text-xs">Change</Text>
                    </TouchableOpacity>
                  )}
              </View>
            )}

            {gameRoom.trumpIndicatorCardId && gameRoom.highestBidder === playerId && (
              <Text className="text-xs text-red-700 mt-1">🎯 Trump Indicator Rules Apply</Text>
            )}
            {gameRoom.currentBid >= 250 && gameRoom.highestBidder === playerId && gameRoom.trumpSuit && (
              <Text className="text-xs text-orange-700 mt-1">⚡ 250+ Bid Rules Apply</Text>
            )}
            {gameRoom.gameState === 'in_progress' && (
              <Text className="text-xs text-gray-700 mt-1">🤫 No Communication</Text>
            )}
          </View>
        </View>
      )} */}

      {/* Central Playing Area - Improved Layout */}
      <View className="absolute inset-0 flex justify-center items-center">
        {/* Enhanced Playing Card Area with better spacing */}
        <View className="relative w-[300px] h-[200px]">
          {/* Top Player Card */}
          <View className="absolute top-[-15%] left-1/2 transform -translate-x-1/2">
            {playedCardsByPosition.top && (
              <Animated.View
                style={{
                  transform: [
                    {
                      scale: centerAnimations.top.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0.3, 1],
                      }),
                    },
                    {
                      translateY: centerAnimations.top.interpolate({
                        inputRange: [0, 1],
                        outputRange: [-60, 0],
                      }),
                    },
                    {
                      scale: clearAnimations.top.interpolate({
                        inputRange: [0, 1],
                        outputRange: [1, 0.1],
                      }),
                    },
                    {
                      rotate: clearAnimations.top.interpolate({
                        inputRange: [0, 1],
                        outputRange: ["0deg", "45deg"],
                      }),
                    },
                    {
                      translateX: clearAnimations.top.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0, width * 0.3],
                      }),
                    },
                    {
                      translateY: clearAnimations.top.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0, height * 0.2],
                      }),
                    },
                  ],
                  opacity: centerAnimations.top.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0, 1],
                  }),
                }}
              >
                <View>
                  {renderPlayedCard(playedCardsByPosition.top, "top")}
                </View>
              </Animated.View>
            )}
          </View>

          {/* Left Player Card */}
          <View className={getLeftPlayerPositionStyle()}>
            {playedCardsByPosition.left && (
              <Animated.View
                style={{
                  transform: [
                    {
                      scale: centerAnimations.left.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0.3, 1],
                      }),
                    },
                    {
                      translateX: centerAnimations.left.interpolate({
                        inputRange: [0, 1],
                        outputRange: [-60, 0],
                      }),
                    },
                    {
                      scale: clearAnimations.left.interpolate({
                        inputRange: [0, 1],
                        outputRange: [1, 0.1],
                      }),
                    },
                    {
                      rotate: clearAnimations.left.interpolate({
                        inputRange: [0, 1],
                        outputRange: ["0deg", "45deg"],
                      }),
                    },
                    {
                      translateX: clearAnimations.left.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0, width * 0.5],
                      }),
                    },
                    {
                      translateY: clearAnimations.left.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0, height * 0.3],
                      }),
                    },
                  ],
                  opacity: centerAnimations.left.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0, 1],
                  }),
                }}
              >
                <View>
                  {renderPlayedCard(playedCardsByPosition.left, "left")}
                </View>
              </Animated.View>
            )}
          </View>

          {/* Right Player Card */}
          <View className="absolute right-[20%] top-[25%] transform -translate-y-1/2">
            {playedCardsByPosition.right && (
              <Animated.View
                style={{
                  transform: [
                    {
                      scale: centerAnimations.right.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0.3, 1],
                      }),
                    },
                    {
                      translateX: centerAnimations.right.interpolate({
                        inputRange: [0, 1],
                        outputRange: [60, 0],
                      }),
                    },
                    {
                      scale: clearAnimations.right.interpolate({
                        inputRange: [0, 1],
                        outputRange: [1, 0.1],
                      }),
                    },
                    {
                      rotate: clearAnimations.right.interpolate({
                        inputRange: [0, 1],
                        outputRange: ["0deg", "45deg"],
                      }),
                    },
                    {
                      translateX: clearAnimations.right.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0, width * 0.3],
                      }),
                    },
                    {
                      translateY: clearAnimations.right.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0, height * 0.3],
                      }),
                    },
                  ],
                  opacity: centerAnimations.right.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0, 1],
                  }),
                }}
              >
                <View>
                  {renderPlayedCard(playedCardsByPosition.right, "right")}
                </View>
              </Animated.View>
            )}
          </View>

          {/* Bottom Player Card */}
          <View className="absolute bottom-[35%] left-1/2 transform -translate-x-1/2">
            {playedCardsByPosition.bottom && (
              <Animated.View
                style={{
                  transform: [
                    {
                      scale: centerAnimations.bottom.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0.3, 1],
                      }),
                    },
                    {
                      translateY: centerAnimations.bottom.interpolate({
                        inputRange: [0, 1],
                        outputRange: [60, 0],
                      }),
                    },
                    {
                      scale: clearAnimations.bottom.interpolate({
                        inputRange: [0, 1],
                        outputRange: [1, 0.1],
                      }),
                    },
                    {
                      rotate: clearAnimations.bottom.interpolate({
                        inputRange: [0, 1],
                        outputRange: ["0deg", "45deg"],
                      }),
                    },
                    {
                      translateX: clearAnimations.bottom.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0, width * 0.4],
                      }),
                    },
                    {
                      translateY: clearAnimations.bottom.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0, height * 0.2],
                      }),
                    },
                  ],
                  opacity: centerAnimations.bottom.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0, 1],
                  }),
                }}
              >
                <View>
                  {renderPlayedCard(playedCardsByPosition.bottom, "bottom")}
                </View>
              </Animated.View>
            )}
          </View>
        </View>
      </View>

      {/* Trump Card Display - Bottom Right, Smaller */}
      {gameRoom.trumpCard && (
        <SafeAreaView className="absolute bottom-4 right-4 z-10">
          <TouchableOpacity
            onPress={handleTrumpCardToggle}
            disabled={gameRoom.highestBidder !== playerId}
            className={`bg-white/95 rounded-lg p-2 border-2 border-yellow-400 shadow-lg ${
              gameRoom.highestBidder === playerId ? "opacity-100" : "opacity-80"
            }`}
          >
            <View className="flex-row items-center justify-center mb-1">
              <Text className="text-xs text-center text-yellow-800 font-bold">
                TRUMP
              </Text>
              {/* {gameRoom.highestBidder === playerId && (
                <Text className="text-xs ml-1">👆</Text>
              )} */}
            </View>
            <View className="flex justify-center items-center">
              <Image
                source={
                  // For bid winner: show based on local toggle state
                  gameRoom.highestBidder === playerId
                    ? isTrumpCardVisible
                      ? getCardById(gameRoom.trumpCard.id).image
                      : cardBack
                    : // For others: show based on global reveal state
                    gameRoom.isTrumpRevealed
                    ? getCardById(gameRoom.trumpCard.id).image
                    : cardBack
                }
                style={{
                  width: cardWidth * 0.6,
                  height: cardHeight * 0.6,
                }}
                resizeMode="contain"
              />
              {/* Trump indicator */}
              {((gameRoom.highestBidder === playerId && isTrumpCardVisible) ||
                (gameRoom.highestBidder !== playerId &&
                  gameRoom.isTrumpRevealed)) && (
                <View className="absolute -top-1 -right-1 bg-yellow-400 rounded-full p-0.5">
                  <Text className="text-xs">👑</Text>
                </View>
              )}
            </View>
            {/* Show instruction for bid winner */}
            {gameRoom.highestBidder === playerId && (
              <Text className="text-xs text-center text-gray-600 mt-1">
                {isTrumpCardVisible ? "Tap to hide" : "Tap to reveal"}
              </Text>
            )}
          </TouchableOpacity>
        </SafeAreaView>
      )}

      {/* Bottom Player Cards (Current Player - Always Your Cards) */}
      {playersByPosition.bottom && (
        <SafeAreaView className="absolute bottom-[-1px] left-0 right-0 flex-row justify-center">
          <View className="flex-row">
            {/* Player Name Indicator */}
            <View className="absolute -top-8 left-1/2 transform -translate-x-1/2">
              {getEnhancedPlayerDisplay(playersByPosition.bottom)}
            </View>
            {sortPlayerCards(playersByPosition.bottom.cards).map(
              (card, index) => {
                const isTurnToPlay =
                  gameRoom.gameState === "in_progress" &&
                  gameRoom.currentTurn === playerId;
                const isTrumpSelection =
                  gameRoom.gameState === "trump_type_selection" &&
                  gameRoom.highestBidder === playerId;

                // For regular play, check if this specific card is playable based on suit following rules
                let isPlayable = false;
                if (isTurnToPlay) {
                  const playableCards = getPlayableCards();
                  isPlayable = playableCards.has(card.id);
                } else if (isTrumpSelection) {
                  isPlayable = true; // All cards playable during trump selection
                }

                // Find the original index of this card in the unsorted array for handleCardClick
                const originalIndex = playersByPosition.bottom!.cards.findIndex(
                  (originalCard) => originalCard.id === card.id
                );

                return renderEnhancedCard(
                  card.id,
                  index,
                  playersByPosition.bottom!,
                  "bottom",
                  isPlayable,
                  () => handleCardClick(originalIndex)
                );
              }
            )}
          </View>
        </SafeAreaView>
      )}

      {/* Right Player Cards */}
      {playersByPosition.right && (
        // <View className="absolute right-2 top-[25%]">
        <SafeAreaView
          className="absolute"
          style={{
            right: isIPhone() ? 2 : 60, // right-2 = 8px
            top: isIPhone() ? "30%" : "40%", // Adjust based on device
          }}
        >
          <View className="relative">
            {/* Player Name Indicator */}
            <View className="absolute -right-10 top-[40%] -rotate-90">
              {getEnhancedPlayerDisplay(playersByPosition.right)}
            </View>
            {/* Container for overlapping cards */}
            <View
              style={{ height: playersByPosition.right.cards.length * 15 + 90 }}
            >
              {playersByPosition.right.cards.map((card, index) => (
                <View
                  key={`right-${index}`}
                  className="absolute"
                  style={{
                    top: index * 15,
                    zIndex: index,
                  }}
                >
                  {renderEnhancedCard(
                    card.id,
                    index,
                    playersByPosition.right!,
                    "right",
                    false
                  )}
                </View>
              ))}
            </View>
          </View>
        </SafeAreaView>
      )}

      {/* Top Player Cards */}
      {playersByPosition.top && (
        <SafeAreaView className="absolute top-[-80px] left-0 right-0 flex-row justify-center">
          <View className="relative">
            {/* Player Name Indicator */}
            <View className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
              {getEnhancedPlayerDisplay(playersByPosition.top)}
            </View>
            <View className="flex-row justify-center items-center">
              {playersByPosition.top.cards.map((card, index) => (
                <View
                  key={`top-${index}`}
                  className="relative"
                  style={{
                    marginLeft: index > 0 ? -45 : 0, // Overlap cards by 45px
                    zIndex: index, // Stack cards properly
                  }}
                >
                  {renderEnhancedCard(
                    card.id,
                    index,
                    playersByPosition.top!,
                    "top",
                    false
                  )}
                </View>
              ))}
            </View>
          </View>
        </SafeAreaView>
      )}

      {/* Left Player Cards */}
      {playersByPosition.left && (
        // <View className="absolute left-[-100px] top-[25%]">
        <SafeAreaView
          className="absolute"
          style={{
            left: isIPhone() ? -10 : -30, // left-[-100px] = -100px
            top: isIPhone() ? "25%" : "40%", // top-[25%] = 25%
          }}
        >
          <View className="relative">
            {/* Player Name Indicator */}
            <View className="absolute right-[-165px] top-[50%] rotate-90">
              {getEnhancedPlayerDisplay(playersByPosition.left)}
            </View>
            {/* Container for overlapping cards */}
            <View
              style={{ height: playersByPosition.left.cards.length * 15 + 90 }}
            >
              {playersByPosition.left.cards.map((card, index) => (
                <View
                  key={`left-${index}`}
                  className="absolute"
                  style={{
                    bottom: index * 15,
                    zIndex: index,
                  }}
                >
                  {renderEnhancedCard(
                    card.id,
                    index,
                    playersByPosition.left!,
                    "left",
                    false
                  )}
                </View>
              ))}
            </View>
          </View>
        </SafeAreaView>
      )}

      {/* Trump Reveal Animation Effect */}
      {showTrumpRevealEffect && gameRoom?.trumpSuit && (
        <View className="absolute inset-0 z-50 pointer-events-none">
          <Animated.View
            className="absolute inset-0 justify-center items-center"
            style={{
              opacity: trumpRevealAnimation.interpolate({
                inputRange: [0, 0.3, 0.8, 1],
                outputRange: [0, 1, 1, 0], // Hold at full opacity longer
              }),
            }}
          >
            {/* Glowing background effect */}
            <Animated.View
              className="absolute inset-0 bg-yellow-400/30"
              style={{
                opacity: trumpRevealAnimation.interpolate({
                  inputRange: [0, 0.2, 0.8, 1],
                  outputRange: [0, 0.8, 0.8, 0], // Hold background glow longer
                }),
              }}
            />

            {/* Main trump reveal card */}
            <Animated.View
              className="bg-white rounded-2xl p-6 border-4 border-yellow-400 shadow-2xl"
              style={{
                transform: [
                  {
                    scale: trumpRevealAnimation.interpolate({
                      inputRange: [0, 0.4, 0.8, 1],
                      outputRange: [0.3, 1.2, 1, 0.8], // Scale down slightly at the end
                    }),
                  },
                  {
                    rotate: trumpRevealAnimation.interpolate({
                      inputRange: [0, 0.3, 0.8, 1],
                      outputRange: ["180deg", "360deg", "360deg", "360deg"], // Complete rotation early, then hold
                    }),
                  },
                ],
              }}
            >
              <View className="items-center">
                {/* Trump announcement */}
                <Text className="text-3xl font-bold text-yellow-600 mb-2">
                  🎯 TRUMP REVEALED! 🎯
                </Text>

                {/* Trump suit display */}
                <View className="bg-yellow-100 rounded-xl p-4 items-center">
                  <Text className="text-4xl mb-2">
                    {getTrumpSuitIcon(gameRoom.trumpSuit)}
                  </Text>
                  <Text className="text-2xl font-bold text-yellow-800 capitalize">
                    {gameRoom.trumpSuit}
                  </Text>
                  <Text className="text-sm text-yellow-700 mt-1">
                    is now Trump!
                  </Text>
                </View>

                {/* Sparkle effects */}
                <View className="absolute -top-2 -left-2">
                  <Animated.Text
                    className="text-2xl"
                    style={{
                      opacity: trumpRevealAnimation.interpolate({
                        inputRange: [0, 0.2, 0.8, 1],
                        outputRange: [0, 1, 1, 0], // Show sparkles longer
                      }),
                      transform: [
                        {
                          rotate: trumpRevealAnimation.interpolate({
                            inputRange: [0, 1],
                            outputRange: ["0deg", "720deg"], // More rotation
                          }),
                        },
                      ],
                    }}
                  >
                    ✨
                  </Animated.Text>
                </View>
                <View className="absolute -top-2 -right-2">
                  <Animated.Text
                    className="text-2xl"
                    style={{
                      opacity: trumpRevealAnimation.interpolate({
                        inputRange: [0, 0.2, 0.8, 1],
                        outputRange: [0, 1, 1, 0], // Show sparkles longer
                      }),
                      transform: [
                        {
                          rotate: trumpRevealAnimation.interpolate({
                            inputRange: [0, 1],
                            outputRange: ["0deg", "-720deg"], // More rotation
                          }),
                        },
                      ],
                    }}
                  >
                    ✨
                  </Animated.Text>
                </View>
                <View className="absolute -bottom-2 -left-2">
                  <Animated.Text
                    className="text-2xl"
                    style={{
                      opacity: trumpRevealAnimation.interpolate({
                        inputRange: [0, 0.2, 0.8, 1],
                        outputRange: [0, 1, 1, 0], // Show sparkles longer
                      }),
                      transform: [
                        {
                          rotate: trumpRevealAnimation.interpolate({
                            inputRange: [0, 1],
                            outputRange: ["0deg", "540deg"], // More rotation
                          }),
                        },
                      ],
                    }}
                  >
                    ✨
                  </Animated.Text>
                </View>
                <View className="absolute -bottom-2 -right-2">
                  <Animated.Text
                    className="text-2xl"
                    style={{
                      opacity: trumpRevealAnimation.interpolate({
                        inputRange: [0, 0.2, 0.8, 1],
                        outputRange: [0, 1, 1, 0], // Show sparkles longer
                      }),
                      transform: [
                        {
                          rotate: trumpRevealAnimation.interpolate({
                            inputRange: [0, 1],
                            outputRange: ["0deg", "-540deg"], // More rotation
                          }),
                        },
                      ],
                    }}
                  >
                    ✨
                  </Animated.Text>
                </View>
              </View>
            </Animated.View>
          </Animated.View>
        </View>
      )}

      {/* Bidding Dialog */}
      <BiddingDialog
        visible={
          gameRoom.gameState === "bidding" && gameRoom.biddingTurn === playerId
        }
        currentBid={gameRoom.currentBid}
        playerName={currentPlayer?.name || "Player"}
        bids={gameRoom.bids || []}
        biddingHistory={
          gameRoom.bids
            ? gameRoom.bids.map((bid) => {
                const player = gameRoom.players.find(
                  (p) => p.id === bid.playerId
                );
                const playerName = player?.name || "Unknown";
                return bid.isPassed
                  ? `${playerName}: Pass`
                  : `${playerName}: ${bid.amount}`;
              })
            : []
        }
        highestBidder={gameRoom.highestBidder}
        players={
          gameRoom.players?.map((p) => ({ id: p.id, name: p.name })) || []
        }
        onSubmitBid={handleSubmitBid}
        onPass={handlePassBid}
        onClose={() => {}} // Can't close during bidding
      />

      {/* Trump Game Type Selection Dialog */}
      <TrumpGameTypeDialog
        visible={
          gameRoom.gameState === "trump_selection" &&
          gameRoom.highestBidder === playerId
        }
        playerName={currentPlayer?.name || "Player"}
        onSelectGameType={handleSelectTrumpGameType}
        onClose={() => {}} // Can't close during trump type selection
      />

      {/* Trump Card Selection Dialog */}
      <TrumpSelectionDialog
        visible={
          gameRoom.gameState === "trump_type_selection" &&
          gameRoom.highestBidder === playerId
        }
        playerName={currentPlayer?.name || "Player"}
        cards={currentPlayer?.cards || []}
        biddingType={gameRoom.biddingType || "four_card"}
        onSelectTrump={handleSelectTrump}
        onClose={() => {}} // Can't close during trump card selection
      />

      {/* Scoring Validation Dialog - Show to all players */}
      {gameRoom.scoringDialogState?.visible === true &&
        gameRoom.scoringDialogState?.scoreValidation && (
        <ScoringValidationDialog
          visible={true}
          scoreValidation={gameRoom.scoringDialogState.scoreValidation}
          onAutoNextRound={handleAutoNextRound}
          pendingDecision={gameRoom.scoringDialogState.pendingDecision}
        />
      )}
    </SafeAreaView>
  );
}

export default GameBoard;
